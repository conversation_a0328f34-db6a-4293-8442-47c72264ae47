2025-05-27 09:45:26,856 - INFO - [INFO create_lmstudio_llm] Using model name 'openai/qwen/qwen3-14b' for LM Studio via litellm.
2025-05-27 09:45:26,867 - INFO - 
LiteLLM completion() model= qwen/qwen3-14b; provider = openai
2025-05-27 09:46:50,252 - INFO - HTTP Request: POST http://127.0.0.1:1234/v1/chat/completions "HTTP/1.1 200 OK"
2025-05-27 09:46:50,417 - INFO - Wrapper: Completed Call, calling success_handler
2025-05-27 09:46:50,420 - INFO - selected model name for cost calculation: openai/qwen/qwen3-14b
2025-05-27 09:46:50,420 - INFO - selected model name for cost calculation: openai/qwen/qwen3-14b
2025-05-27 09:46:50,421 - INFO - selected model name for cost calculation: qwen/qwen3-14b
2025-05-27 09:46:50,422 - INFO - selected model name for cost calculation: openai/qwen/qwen3-14b
2025-05-27 09:46:50,430 - INFO - selected model name for cost calculation: openai/qwen/qwen3-14b
2025-05-27 09:46:50,438 - INFO - selected model name for cost calculation: qwen/qwen3-14b

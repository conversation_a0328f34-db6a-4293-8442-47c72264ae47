@echo off
echo ========================================
echo    CrewAI-Studio avec LM Studio DIRECT
echo    (Sans proxy litellm)
echo ========================================
echo.

echo IMPORTANT: Assurez-vous que LM Studio est demarré avec votre modele charge !
echo.
pause

echo Demarrage de CrewAI-Studio...
echo.
echo CONFIGURATION DANS L'INTERFACE :
echo - Fournisseur LLM : LM Studio
echo - Modele : openai/qwen/qwen3-14b
echo.
echo CrewAI-Studio va s'ouvrir dans votre navigateur...
echo.
streamlit run app/app.py

echo.
echo Fermeture du systeme...
pause

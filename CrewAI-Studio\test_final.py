#!/usr/bin/env python3
"""
Test final pour vérifier que LM Studio fonctionne avec CrewAI
"""
import os
import sys
sys.path.append('app')

from llms import create_lmstudio_llm
from dotenv import load_dotenv

def test_final_lmstudio():
    """Test final de la configuration LM Studio"""
    
    print("🔍 Test final de la configuration LM Studio + CrewAI...")
    print("=" * 60)
    
    # Charger les variables d'environnement
    load_dotenv()
    
    # Simuler session_state pour le test
    class MockSessionState:
        def __init__(self):
            self.env_vars = {
                'LMSTUDIO_API_BASE': 'http://127.0.0.1:1234/v1'
            }
    
    # Mock streamlit session_state
    import streamlit as st
    st.session_state = MockSessionState()
    
    try:
        print("📡 Test de création d'instance LLM...")
        llm = create_lmstudio_llm("qwen/qwen3-14b", 0.7)
        print("✅ Instance LLM créée avec succès")
        print(f"📋 Type: {type(llm)}")
        print(f"📋 Modèle: {llm.model}")
        print(f"📋 Température: {llm.temperature}")
        
        print("\n🚀 Test d'appel LLM...")
        # Test simple avec la classe LLM de CrewAI
        response = llm.call("Bonjour ! Dis-moi juste 'Salut' en réponse.")
        print("✅ SUCCÈS ! Réponse reçue :")
        print(f"📝 {response}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR : {e}")
        print(f"🔧 Vérifications à faire :")
        print(f"   1. LM Studio est-il ouvert ?")
        print(f"   2. Le modèle qwen/qwen3-14b est-il chargé ?")
        print(f"   3. Le serveur local est-il démarré ?")
        print(f"   4. L'URL http://127.0.0.1:1234/v1/models fonctionne-t-elle ?")
        
        return False

if __name__ == "__main__":
    success = test_final_lmstudio()
    if success:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("🚀 CrewAI-Studio devrait maintenant fonctionner parfaitement avec LM Studio.")
        print("\n📋 Configuration à utiliser dans l'interface :")
        print("   - Fournisseur LLM : LM Studio")
        print("   - Modèle : qwen/qwen3-14b")
    else:
        print("\n❌ TESTS ÉCHOUÉS.")
        print("🔧 Vérifiez LM Studio avant de lancer CrewAI-Studio.")
    
    input("\nAppuyez sur Entrée pour fermer...")

import sqlite3
import os
import json
from my_tools import TOOL_CLASSES
from sqlalchemy import create_engine, text

# If you have an environment variable DB_URL for Postgres, use that.
# Otherwise, fallback to local SQLite file: 'sqlite:///crewai.db'
DEFAULT_SQLITE_URL = 'sqlite:///crewai.db'
DB_URL = os.getenv('DB_URL', DEFAULT_SQLITE_URL)

# Create a SQLAlchemy Engine.
# For example, DB_URL could be:
#   "********************************************/dbname"
# or fallback to: "sqlite:///crewai.db"
engine = create_engine(DB_URL, echo=False)

def get_db_connection():
    # conn = sqlite3.connect(DB_NAME)
    # conn.row_factory = sqlite3.Row
    # return conn
    """
    Return a context-managed connection from the SQLAlchemy engine.
    """
    return engine.connect()

def create_tables():
    create_sql = text('''
        CREATE TABLE IF NOT EXISTS entities (
            id TEXT PRIMARY KEY,
            entity_type TEXT,
            data TEXT
        )
    ''')
    with get_db_connection() as conn:
        conn.execute(create_sql)
        conn.commit()

def migrate_agents_defaults():
    """
    Migrate agent data to clear old default values for role, backstory, and goal.
    This function should be run once during initialization.
    """
    # Check if migration has already been run in this session
    import streamlit as st
    if hasattr(st.session_state, 'agents_migration_done') and st.session_state.agents_migration_done:
        return

    from my_agent import MyAgent # Import here to avoid circular dependency
    print("Running agent defaults migration...")
    rows = load_entities('agent')
    agents_to_update = []
    for row in rows:
        data = row[1]
        agent_id = row[0]
        needs_update = False
        if data.get('role') == "Senior Researcher":
            data['role'] = ""
            needs_update = True
        if data.get('backstory') == "Driven by curiosity, you're at the forefront of innovation, eager to explore and share knowledge that could change the world.":
            data['backstory'] = ""
            needs_update = True
        if data.get('goal') == "Uncover groundbreaking technologies in AI":
            data['goal'] = ""
            needs_update = True

        if needs_update:
            # Create a temporary MyAgent object to use the save_agent function
            # This is a bit hacky, but reuses existing save logic
            # A more robust migration would update the 'entities' table directly
            # For simplicity and re-using save_agent, we'll do this for now.
            # Note: This assumes MyAgent can be initialized with just id and data
            # If not, we'd need to load full agent data first.
            # Let's load the full agent data to be safe.
            # Re-loading all agents here is inefficient for a large DB, but safe.
            # A better approach for large DBs would be a direct SQL update.
            # Given the context, assuming a small local DB where this is acceptable.
            print(f"Agent {agent_id} needs update. Loading full agent data...")
            # Find the agent in the list loaded by load_entities
            # This is still not ideal. Let's rethink.
            # The load_agents function already does the in-memory clearing.
            # We need to save the *cleared* agents back to the DB.
            # Let's modify load_agents to return agents and a flag if migration is needed.
            # Or, simply iterate and save if defaults are found.

            # Let's try iterating through loaded agents and saving if defaults are found.
            # This requires load_agents to return MyAgent objects.
            # load_agents already returns MyAgent objects.
            # So, we can load, check defaults, and save if needed.
    # Load raw data directly from entities table for migration
    rows = load_entities('agent')
    for agent_id, data in rows:
        needs_update = False
        if data.get('role') == "Senior Researcher":
            data['role'] = ""
            needs_update = True
        if data.get('backstory') == "Driven by curiosity, you're at the forefront of innovation, eager to explore and share knowledge that could change the world.":
            data['backstory'] = ""
            needs_update = True
        if data.get('goal') == "Uncover groundbreaking technologies in AI":
            data['goal'] = ""
            needs_update = True

        if needs_update:
            print(f"Migrating agent {agent_id}: Clearing default values in DB.")
            save_entity('agent', agent_id, data) # Save the updated raw data back to the database

    print("Agent defaults migration finished.")

    # Mark migration as done for this session
    import streamlit as st
    st.session_state.agents_migration_done = True

def initialize_db():
    """
    Initialize the database by creating tables if they do not exist.
    Also runs necessary migrations.
    """
    create_tables()
    migrate_agents_defaults() # Run migration after creating tables


def save_entity(entity_type, entity_id, data):
    # For SQLite ≥ 3.24 and for Postgres, we can do:
    #   INSERT ... ON CONFLICT(id) DO UPDATE ...
    # to emulate "INSERT OR REPLACE"
    upsert_sql = text('''
        INSERT INTO entities (id, entity_type, data)
        VALUES (:id, :etype, :data)
        ON CONFLICT(id) DO UPDATE
            SET entity_type = EXCLUDED.entity_type,
                data = EXCLUDED.data
    ''')
    with get_db_connection() as conn:
        conn.execute(
            upsert_sql,
            {
                "id": entity_id,
                "etype": entity_type,
                "data": json.dumps(data),
            }
        )
        conn.commit()

def load_entities(entity_type):
    query = text('SELECT id, data FROM entities WHERE entity_type = :etype')
    with get_db_connection() as conn:
        result = conn.execute(query, {"etype": entity_type})
        # result.mappings() gives us rows as dicts (if using SQLAlchemy 1.4+)
        rows = result.mappings().all()
    return [(row["id"], json.loads(row["data"])) for row in rows]

def delete_entity(entity_type, entity_id):
    delete_sql = text('''
        DELETE FROM entities
        WHERE id = :id AND entity_type = :etype
    ''')
    with get_db_connection() as conn:
        conn.execute(delete_sql, {"id": entity_id, "etype": entity_type})
        conn.commit()

def save_tools_state(enabled_tools):
    data = {
        'enabled_tools': enabled_tools
    }
    save_entity('tools_state', 'enabled_tools', data)

def load_tools_state():
    rows = load_entities('tools_state')
    if rows:
        return rows[0][1].get('enabled_tools', {})
    return {}

def save_knowledge_source(knowledge_source):
    data = {
        'name': knowledge_source.name,
        'source_type': knowledge_source.source_type,
        'source_path': knowledge_source.source_path,
        'content': knowledge_source.content,
        'metadata': knowledge_source.metadata,
        'chunk_size': knowledge_source.chunk_size,
        'chunk_overlap': knowledge_source.chunk_overlap,
        'created_at': knowledge_source.created_at
    }
    save_entity('knowledge_source', knowledge_source.id, data)

def load_knowledge_sources():
    from my_knowledge_source import MyKnowledgeSource
    rows = load_entities('knowledge_source')
    knowledge_sources = []
    for row in rows:
        data = row[1]
        knowledge_source = MyKnowledgeSource(id=row[0], **data)
        knowledge_sources.append(knowledge_source)
    return sorted(knowledge_sources, key=lambda x: x.created_at)

def delete_knowledge_source(knowledge_source_id):
    delete_entity('knowledge_source', knowledge_source_id)

def save_agent(agent):
    data = {
        'created_at': agent.created_at,
        'role': agent.role,
        'backstory': agent.backstory,
        'goal': agent.goal,
        'allow_delegation': agent.allow_delegation,
        'verbose': agent.verbose,
        'cache': agent.cache,
        'llm_provider_model': agent.llm_provider_model,
        'temperature': agent.temperature,
        'max_iter': agent.max_iter,
        'tool_ids': [tool.tool_id for tool in agent.tools],
        'knowledge_source_ids': agent.knowledge_source_ids
    }
    save_entity('agent', agent.id, data)

def load_agents():
    from my_agent import MyAgent
    rows = load_entities('agent')
    tools_dict = {tool.tool_id: tool for tool in load_tools()}
    agents = []
    for row in rows:
        data = row[1]
        # Check and clear old default values if they exist in the database
        if data.get('role') == "Senior Researcher":
            data['role'] = ""
        if data.get('backstory') == "Driven by curiosity, you're at the forefront of innovation, eager to explore and share knowledge that could change the world.":
            data['backstory'] = ""
        if data.get('goal') == "Uncover groundbreaking technologies in AI":
            data['goal'] = ""

        tool_ids = data.pop('tool_ids', [])
        knowledge_source_ids = data.pop('knowledge_source_ids', [])
        agent = MyAgent(id=row[0], knowledge_source_ids=knowledge_source_ids, **data)
        agent.tools = [tools_dict[tool_id] for tool_id in tool_ids if tool_id in tools_dict]
        agents.append(agent)
    return sorted(agents, key=lambda x: x.created_at)


def delete_agent(agent_id):
    delete_entity('agent', agent_id)

def save_task(task):
    data = {
        'description': task.description,
        'expected_output': task.expected_output,
        'async_execution': task.async_execution,
        'agent_id': task.agent.id if task.agent else None,
        'context_from_async_tasks_ids': task.context_from_async_tasks_ids,
        'context_from_sync_tasks_ids': task.context_from_sync_tasks_ids,
        'created_at': task.created_at
    }
    save_entity('task', task.id, data)

def load_tasks():
    from my_task import MyTask
    rows = load_entities('task')
    agents_dict = {agent.id: agent for agent in load_agents()}
    tasks = []
    for row in rows:
        data = row[1]
        agent_id = data.pop('agent_id', None)
        task = MyTask(id=row[0], agent=agents_dict.get(agent_id), **data)
        tasks.append(task)
    return sorted(tasks, key=lambda x: x.created_at)

def delete_task(task_id):
    delete_entity('task', task_id)

def save_crew(crew):
    data = {
        'name': crew.name,
        'process': crew.process,
        'verbose': crew.verbose,
        'agent_ids': [agent.id for agent in crew.agents],
        'task_ids': [task.id for task in crew.tasks],
        'memory': crew.memory,
        'cache': crew.cache,
        'planning': crew.planning,
        'max_rpm': crew.max_rpm,
        'manager_llm': crew.manager_llm,
        'manager_agent_id': crew.manager_agent.id if crew.manager_agent else None,
        'created_at': crew.created_at,
        'knowledge_source_ids': crew.knowledge_source_ids  # Add this line
    }
    save_entity('crew', crew.id, data)

def load_crews():
    from my_crew import MyCrew
    rows = load_entities('crew')
    agents_dict = {agent.id: agent for agent in load_agents()}
    tasks_dict = {task.id: task for task in load_tasks()}
    crews = []
    for row in rows:
        data = row[1]
        crew = MyCrew(
            id=row[0],
            name=data['name'],
            process=data['process'],
            verbose=data['verbose'],
            created_at=data['created_at'],
            memory=data.get('memory'),
            cache=data.get('cache'),
            planning=data.get('planning'),
            max_rpm=data.get('max_rpm'),
            manager_llm=data.get('manager_llm'),
            manager_agent=agents_dict.get(data.get('manager_agent_id')),
            knowledge_source_ids=data.get('knowledge_source_ids', [])  # Add this line
        )
        crew.agents = [agents_dict[agent_id] for agent_id in data['agent_ids'] if agent_id in agents_dict]
        crew.tasks = [tasks_dict[task_id] for task_id in data['task_ids'] if task_id in tasks_dict]
        crews.append(crew)
    return sorted(crews, key=lambda x: x.created_at)

def delete_crew(crew_id):
    delete_entity('crew', crew_id)

def save_tool(tool):
    data = {
        'name': tool.name,
        'description': tool.description,
        'parameters': tool.get_parameters()
    }
    save_entity('tool', tool.tool_id, data)

def load_tools():
    rows = load_entities('tool')
    tools = []
    for row in rows:
        data = row[1]
        tool_class = TOOL_CLASSES[data['name']]
        tool = tool_class(tool_id=row[0])
        tool.set_parameters(**data['parameters'])
        tools.append(tool)
    return tools

def delete_tool(tool_id):
    delete_entity('tool', tool_id)

def export_to_json(file_path):
    with get_db_connection() as conn:
        # Use SQLAlchemy's text() for raw SQL
        query = text('SELECT * FROM entities')
        result = conn.execute(query)

        # Convert to list of dictionaries
        rows = [
            {
                'id': row.id,
                'entity_type': row.entity_type,
                'data': json.loads(row.data)
            }
            for row in result
        ]

        # Write to file
        with open(file_path, 'w') as f:
            json.dump(rows, f, indent=4)

def import_from_json(file_path):
    with open(file_path, 'r') as f:
        data = json.load(f)

    with get_db_connection() as conn:
        for entity in data:
            # Use SQLAlchemy's text() for raw SQL with parameters
            upsert_sql = text('''
                INSERT INTO entities (id, entity_type, data)
                VALUES (:id, :etype, :data)
                ON CONFLICT(id) DO UPDATE
                    SET entity_type = EXCLUDED.entity_type,
                        data = EXCLUDED.data
            ''')

            conn.execute(
                upsert_sql,
                {
                    "id": entity['id'],
                    "etype": entity['entity_type'],
                    "data": json.dumps(entity['data'])
                }
            )

        conn.commit()

def save_result(result):
    """Save a result to the database."""
    data = {
        'crew_id': result.crew_id,
        'crew_name': result.crew_name,
        'inputs': result.inputs,
        'result': result.result,
        'created_at': result.created_at
    }
    save_entity('result', result.id, data)

def load_results():
    """Load all results from the database."""
    from result import Result
    rows = load_entities('result')
    results = []
    for row in rows:
        data = row[1]
        result = Result(
            id=row[0],
            crew_id=data['crew_id'],
            crew_name=data['crew_name'],
            inputs=data['inputs'],
            result=data['result'],
            created_at=data['created_at']
        )
        results.append(result)
    return sorted(results, key=lambda x: x.created_at, reverse=True)

def delete_result(result_id):
    """Delete a result from the database."""
    delete_entity('result', result_id)
#!/usr/bin/env python3
"""
Script de test pour vérifier la connexion à LM Studio
"""
import requests
import json

def test_lmstudio_connection():
    """Test la connexion à LM Studio"""
    
    # Ports communs pour LM Studio
    ports_to_test = [1234, 1235, 8080, 8000]
    
    print("🔍 Test de connexion à LM Studio...")
    print("=" * 50)
    
    for port in ports_to_test:
        url = f"http://localhost:{port}/v1/models"
        print(f"\n📡 Test du port {port}...")
        
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ SUCCÈS ! LM Studio trouvé sur le port {port}")
                print(f"📋 Modèles disponibles :")
                
                if 'data' in data:
                    for model in data['data']:
                        print(f"   - {model.get('id', 'Nom inconnu')}")
                else:
                    print("   - Aucun modèle trouvé dans la réponse")
                
                print(f"\n🔧 Configuration à utiliser :")
                print(f"   LMSTUDIO_API_BASE=http://localhost:{port}/v1")
                return port
                
        except requests.exceptions.ConnectionError:
            print(f"❌ Pas de connexion sur le port {port}")
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout sur le port {port}")
        except Exception as e:
            print(f"❌ Erreur sur le port {port}: {e}")
    
    print(f"\n❌ Aucun serveur LM Studio trouvé !")
    print(f"\n💡 Vérifications à faire :")
    print(f"   1. LM Studio est-il ouvert ?")
    print(f"   2. Un modèle est-il chargé ?")
    print(f"   3. Le serveur local est-il démarré ?")
    print(f"   4. Vérifiez l'onglet 'Local Server' dans LM Studio")
    
    return None

if __name__ == "__main__":
    test_lmstudio_connection()
    input("\nAppuyez sur Entrée pour fermer...")

@echo off
echo Demarrage du proxy LiteLLM pour LM Studio...
echo.
echo Assurez-vous que LM Studio est en cours d'execution avec votre modele charge !
echo.
echo Le proxy sera accessible sur http://localhost:8000
echo Pour arreter le proxy, fermez cette fenetre ou appuyez sur Ctrl+C
echo.
litellm --model openai/gpt-3.5-turbo --api_base http://localhost:1234/v1 --api_key lm-studio --port 8000 --host 0.0.0.0
pause

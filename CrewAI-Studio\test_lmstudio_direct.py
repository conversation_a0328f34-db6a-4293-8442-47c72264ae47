#!/usr/bin/env python3
"""
Test direct de connexion à LM Studio avec ChatOpenAI
"""
from langchain_openai import ChatOpenAI
from langchain.schema import HumanMessage

def test_lmstudio_direct():
    """Test direct de LM Studio avec ChatOpenAI"""
    
    print("🔍 Test de connexion directe à LM Studio...")
    print("=" * 50)
    
    try:
        # Configuration directe
        llm = ChatOpenAI(
            model="qwen/qwen3-14b",
            temperature=0.7,
            base_url="http://127.0.0.1:1234/v1",
            api_key="lm-studio",
            max_tokens=100,
            timeout=30
        )
        
        print("✅ Instance ChatOpenAI créée avec succès")
        
        # Test simple
        message = HumanMessage(content="Bonjour ! Peux-tu me dire ton nom en une phrase ?")
        print("📤 Envoi du message de test...")
        
        response = llm.invoke([message])
        print("✅ SUCCÈS ! Réponse reçue :")
        print(f"📝 {response.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ ERREUR : {e}")
        print(f"🔧 Vérifications à faire :")
        print(f"   1. LM Studio est-il ouvert ?")
        print(f"   2. Le modèle qwen/qwen3-14b est-il chargé ?")
        print(f"   3. Le serveur local est-il démarré ?")
        print(f"   4. L'URL http://127.0.0.1:1234/v1/models fonctionne-t-elle ?")
        
        return False

if __name__ == "__main__":
    success = test_lmstudio_direct()
    if success:
        print("\n🎉 Le test est réussi ! CrewAI devrait maintenant fonctionner.")
    else:
        print("\n❌ Le test a échoué. Vérifiez LM Studio avant de relancer CrewAI.")
    
    input("\nAppuyez sur Entrée pour fermer...")

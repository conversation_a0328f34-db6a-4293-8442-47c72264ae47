import os
import sys
from dotenv import load_dotenv
import streamlit as st
from langchain_openai import ChatOpenAI
from langchain_groq import Chat<PERSON><PERSON>q
from langchain_anthropic import ChatAnthropic
from crewai import LLM
from langchain_openai.chat_models.base import BaseChatOpenAI
from litellm import completion
import logging
from typing import Dict, Any, Optional, List

# --- Constants ---
DEFAULT_OPENAI_API_BASE = "https://api.openai.com/v1/"
DEFAULT_XAI_API_BASE = "https://api.x.ai/v1"
LM_STUDIO_API_KEY = "lm-studio"
OLLAMA_API_KEY = "ollama"

DEFAULT_REQUEST_TIMEOUT = 60  # Default timeout in seconds
DEFAULT_MAX_TOKENS = 4095

# Environment variable names
ENV_OPENAI_API_KEY = "OPENAI_API_KEY"
ENV_OPENAI_API_BASE = "OPENAI_API_BASE"
ENV_GROQ_API_KEY = "GROQ_API_KEY"
ENV_LMSTUDIO_API_BASE = "LMSTUDIO_API_BASE"
ENV_ANTHROPIC_API_KEY = "ANTHROPIC_API_KEY"
ENV_OLLAMA_HOST = "OLLAMA_HOST"
ENV_XAI_API_KEY = "XAI_API_KEY"
ENV_REQUEST_TIMEOUT = "REQUEST_TIMEOUT"
ENV_MAX_TOKENS = "MAX_TOKENS"

# Models
OPENAI_MODELS = os.getenv("OPENAI_PROXY_MODELS", "gpt-4.1-mini,gpt-4o-mini,gpt-4o,gpt-3.5-turbo,gpt-4-turbo").split(",")
GROQ_MODELS = ["groq/llama3-8b-8192", "groq/llama3-70b-8192", "groq/mixtral-8x7b-32768"]
OLLAMA_MODELS = os.getenv("OLLAMA_MODELS", "").split(",") if os.getenv("OLLAMA_MODELS") else []
ANTHROPIC_MODELS = ["claude-3-5-sonnet-20240620", "claude-3-7-sonnet-20250219"]
LM_STUDIO_MODELS = ["qwen/qwen3-14b", "mistralai/devstral-small-2505", "mistralai/mistral-7b-instruct-v0.3", "google/gemma-3-12b", "microsoft/phi-4-reasoning-plus", "qwen/qwen3-8b"] # This could also be made configurable via env var
XAI_MODELS = ["xai/grok-2-1212", "xai/grok-beta"]

# Configure logging to a file
logging.basicConfig(filename='app_debug.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# ✅ Chargement des variables d'environnement dès le départ
def load_secrets_from_env() -> None:
    """Loads environment variables from .env file and populates st.session_state.env_vars."""
    load_dotenv(override=True)
    logging.debug(f"In load_secrets_from_env: os.getenv('{ENV_LMSTUDIO_API_BASE}') after load_dotenv = {os.getenv(ENV_LMSTUDIO_API_BASE)}")

    st.session_state.env_vars = {
        ENV_OPENAI_API_KEY: os.getenv(ENV_OPENAI_API_KEY),
        ENV_OPENAI_API_BASE: os.getenv(ENV_OPENAI_API_BASE, DEFAULT_OPENAI_API_BASE),
        ENV_GROQ_API_KEY: os.getenv(ENV_GROQ_API_KEY),
        ENV_LMSTUDIO_API_BASE: os.getenv(ENV_LMSTUDIO_API_BASE),
        ENV_ANTHROPIC_API_KEY: os.getenv(ENV_ANTHROPIC_API_KEY),
        ENV_OLLAMA_HOST: os.getenv(ENV_OLLAMA_HOST),
        ENV_XAI_API_KEY: os.getenv(ENV_XAI_API_KEY),
        ENV_REQUEST_TIMEOUT: os.getenv(ENV_REQUEST_TIMEOUT, str(DEFAULT_REQUEST_TIMEOUT)),
        ENV_MAX_TOKENS: os.getenv(ENV_MAX_TOKENS, str(DEFAULT_MAX_TOKENS)),
    }
    logging.debug(f"In load_secrets_from_env: st.session_state.env_vars['{ENV_LMSTUDIO_API_BASE}'] after populating session state = {st.session_state.env_vars.get(ENV_LMSTUDIO_API_BASE)}")



def switch_environment(new_env_vars: Dict[str, Optional[str]]) -> None:
    """Temporarily switches environment variables for a specific LLM provider."""
    logging.debug(f"Entering switch_environment with: {new_env_vars}")
    for key, value in new_env_vars.items():
        if value is not None:
            os.environ[key] = str(value)  # Ensure value is string
            # Update session_state only if the key is one we manage, to avoid polluting it
            if key in st.session_state.env_vars:
                st.session_state.env_vars[key] = value
            logging.debug(f"Set os.environ[{key}] = {os.environ[key]}")
        elif key in os.environ:
            del os.environ[key]
            logging.debug(f"Deleted os.environ[{key}]")

def restore_environment() -> None:
    """Restores environment variables from st.session_state.env_vars."""
    logging.debug("Restoring environment variables from session state.")
    for key, value in st.session_state.env_vars.items():
        if key in [ENV_OPENAI_API_KEY, ENV_OPENAI_API_BASE, ENV_GROQ_API_KEY, ENV_LMSTUDIO_API_BASE, ENV_ANTHROPIC_API_KEY, ENV_OLLAMA_HOST, ENV_XAI_API_KEY, ENV_REQUEST_TIMEOUT, ENV_MAX_TOKENS]: # Only restore managed keys
            if value is not None:
                os.environ[key] = str(value) # Ensure value is string for os.environ
            elif key in os.environ:
                del os.environ[key]

def safe_pop_env_var(key):
    os.environ.pop(key, None)

def create_openai_llm(model: str, temperature: float) -> LLM:
    """Creates an LLM instance for OpenAI compatible APIs."""
    api_key = st.session_state.env_vars.get(ENV_OPENAI_API_KEY)
    api_base = st.session_state.env_vars.get(ENV_OPENAI_API_BASE, DEFAULT_OPENAI_API_BASE)

    if not api_key:
        raise ValueError(f"{ENV_OPENAI_API_KEY} not set in .env file or Streamlit session state.")

    # Temporarily set environment for this specific call
    switch_environment({
        ENV_OPENAI_API_KEY: api_key,
        ENV_OPENAI_API_BASE: api_base,
    })

    request_timeout = int(st.session_state.env_vars.get(ENV_REQUEST_TIMEOUT, DEFAULT_REQUEST_TIMEOUT))
    max_tokens = int(st.session_state.env_vars.get(ENV_MAX_TOKENS, DEFAULT_MAX_TOKENS))

    # Note: The LLM class from crewai might not directly support request_timeout and max_tokens in its constructor.
    # If these are needed, ChatOpenAI or a similar class that explicitly supports them should be used.
    # For now, assuming LLM handles them via underlying ChatOpenAI or similar, or they are set globally.
    # If LLM is a wrapper, it might pick up base_url and api_key from environment if not passed directly.
    return LLM(model=model, temperature=temperature, base_url=api_base, api_key=api_key) # Pass api_key explicitly if supported

def create_anthropic_llm(model: str, temperature: float) -> ChatAnthropic:
    """Creates an LLM instance for Anthropic."""
    api_key = st.session_state.env_vars.get(ENV_ANTHROPIC_API_KEY)

    if not api_key:
        raise ValueError(f"{ENV_ANTHROPIC_API_KEY} not set in .env file or Streamlit session state.")

    # Anthropic SDK usually takes api_key directly, environment switching might be less critical
    # but kept for consistency if other parts of the chain rely on it.
    switch_environment({ENV_ANTHROPIC_API_KEY: api_key})

    # Anthropic's SDK might have its own timeout/max_tokens parameters or environment variables.
    # For ChatAnthropic, max_tokens_to_sample is a common parameter.
    # We'll use a generic max_tokens from our constants for now.
    max_tokens = int(st.session_state.env_vars.get(ENV_MAX_TOKENS, DEFAULT_MAX_TOKENS))
    # request_timeout for ChatAnthropic might be handled differently, e.g. client(timeout=...)
    # For simplicity, we are not setting a specific timeout here, assuming default or global config.

    return ChatAnthropic(
        anthropic_api_key=api_key,
        model_name=model,
        temperature=temperature,
        max_tokens_to_sample=max_tokens, # Parameter name for Anthropic
    )

def create_groq_llm(model: str, temperature: float) -> ChatGroq:
    """Creates an LLM instance for Groq."""
    api_key = st.session_state.env_vars.get(ENV_GROQ_API_KEY)

    if not api_key:
        raise ValueError(f"{ENV_GROQ_API_KEY} not set in .env file or Streamlit session state.")

    switch_environment({ENV_GROQ_API_KEY: api_key})

    max_tokens = int(st.session_state.env_vars.get(ENV_MAX_TOKENS, DEFAULT_MAX_TOKENS))
    # ChatGroq might not have a direct request_timeout parameter in constructor, may use client config.

    return ChatGroq(
        groq_api_key=api_key,
        model_name=model,
        temperature=temperature,
        max_tokens=max_tokens # ChatGroq uses max_tokens
    )

def create_ollama_llm(model: str, temperature: float) -> LLM:
    """Creates an LLM instance for Ollama (via OpenAI compatible endpoint)."""
    host = st.session_state.env_vars.get(ENV_OLLAMA_HOST)

    if not host:
        raise ValueError(f"{ENV_OLLAMA_HOST} not set in .env file or Streamlit session state.")

    # Ollama is often accessed via an OpenAI-compatible API, so we set these env vars.
    switch_environment({
        ENV_OPENAI_API_KEY: OLLAMA_API_KEY, # Placeholder API key for Ollama
        ENV_OPENAI_API_BASE: host,
    })

    # For LLM class, timeout and max_tokens might be implicitly handled if it wraps ChatOpenAI.
    # If direct control is needed, consider using ChatOpenAI directly as in create_lmstudio_llm.
    # request_timeout = int(st.session_state.env_vars.get(ENV_REQUEST_TIMEOUT, DEFAULT_REQUEST_TIMEOUT))
    # max_tokens = int(st.session_state.env_vars.get(ENV_MAX_TOKENS, DEFAULT_MAX_TOKENS))

    return LLM(
        model=model,
        temperature=temperature,
        base_url=host,
        api_key=OLLAMA_API_KEY # Pass placeholder key
    )

def create_xai_llm(model: str, temperature: float) -> LLM:
    """Creates an LLM instance for Xai (via OpenAI compatible endpoint)."""
    api_key = st.session_state.env_vars.get(ENV_XAI_API_KEY)
    host = DEFAULT_XAI_API_BASE # Using the defined constant

    if not api_key:
        raise ValueError(f"{ENV_XAI_API_KEY} must be set in .env file or Streamlit session state.")

    switch_environment({
        ENV_OPENAI_API_KEY: api_key,
        ENV_OPENAI_API_BASE: host,
    })

    # Similar to Ollama and OpenAI, assuming LLM class handles specifics or uses ChatOpenAI backend.
    # request_timeout = int(st.session_state.env_vars.get(ENV_REQUEST_TIMEOUT, DEFAULT_REQUEST_TIMEOUT))
    # max_tokens = int(st.session_state.env_vars.get(ENV_MAX_TOKENS, DEFAULT_MAX_TOKENS))

    return LLM(
        model=model,
        temperature=temperature,
        api_key=api_key,
        base_url=host
    )

def create_lmstudio_llm(model: str, temperature: float) -> LLM:
    """Creates an LLM instance for LM Studio."""
    api_base = st.session_state.env_vars.get(ENV_LMSTUDIO_API_BASE)

    logging.debug(f"[DEBUG create_lmstudio_llm] LMSTUDIO_API_BASE from session_state: {api_base}")

    if not api_base:
        # Fallback to os.getenv if not in session_state (e.g., direct script run without Streamlit frontend)
        api_base = os.getenv(ENV_LMSTUDIO_API_BASE)
        logging.debug(f"[DEBUG create_lmstudio_llm] LMSTUDIO_API_BASE from os.getenv: {api_base}")

    if not api_base:
        error_message = f"{ENV_LMSTUDIO_API_BASE} is not set in .env file or Streamlit session state."
        logging.error(error_message)
        raise ValueError(error_message)

    # Set environment variables for litellm to recognize LM Studio as OpenAI-compatible
    switch_environment({
        ENV_OPENAI_API_KEY: LM_STUDIO_API_KEY,
        ENV_OPENAI_API_BASE: api_base,
    })

    request_timeout = int(st.session_state.env_vars.get(ENV_REQUEST_TIMEOUT, DEFAULT_REQUEST_TIMEOUT))
    max_tokens = int(st.session_state.env_vars.get(ENV_MAX_TOKENS, DEFAULT_MAX_TOKENS))

    # Keep the openai/ prefix for litellm to recognize it as OpenAI-compatible
    # but ensure we have the right model name
    if not model.startswith("openai/"):
        actual_model_name = f"openai/{model}"
    else:
        actual_model_name = model

    logging.info(f"[INFO create_lmstudio_llm] Using model name '{actual_model_name}' for LM Studio via litellm.")

    # Use CrewAI's LLM class which handles litellm internally
    try:
        return LLM(
            model=actual_model_name, # Use the model name with openai/ prefix
            temperature=temperature,
            base_url=api_base, # Direct base_url for LM Studio
            api_key=LM_STUDIO_API_KEY # Pass the dummy API key
        )

    except Exception as e:
        logging.error(f"LLM creation failed: {e}")
        raise ValueError(f"LLM creation failed: {e}")

LLM_CONFIG: Dict[str, Dict[str, Any]] = {
    "OpenAI": {
        "models": OPENAI_MODELS,
        "create_llm": create_openai_llm,
    },
    "Groq": {
        "models": GROQ_MODELS,
        "create_llm": create_groq_llm,
    },
    "Ollama": {
        "models": OLLAMA_MODELS,
        "create_llm": create_ollama_llm,
    },
    "Anthropic": {
        "models": ANTHROPIC_MODELS,
        "create_llm": create_anthropic_llm,
    },
    "LM Studio": {
        "models": LM_STUDIO_MODELS,
        "create_llm": create_lmstudio_llm,
    },
    "Xai": {
        "models": XAI_MODELS,
        "create_llm": create_xai_llm,
    },
}

def llm_providers_and_models() -> List[str]:
    """Returns a list of available LLM providers and models."""
    return [f"{provider}: {model}" for provider in LLM_CONFIG.keys() for model in LLM_CONFIG[provider]["models"] if model] # Ensure model is not empty string

def create_llm(provider_and_model: str, temperature: float = 0.15) -> Any: # Return type can be BaseChatModel or LLM depending on what create_llm_func returns
    """Creates an LLM instance based on provider and model string."""
    try:
        provider, model = provider_and_model.split(": ", 1)
    except ValueError:
        raise ValueError(f"Invalid provider_and_model format: '{provider_and_model}'. Expected 'Provider: Model'.")

    config = LLM_CONFIG.get(provider)
    if not config:
        raise ValueError(f"LLM provider '{provider}' is not recognized or not supported.")

    create_llm_func = config.get("create_llm")
    if not create_llm_func:
        # This case should ideally not be reached if LLM_CONFIG is well-formed
        raise ValueError(f"Creation function not found for LLM provider '{provider}'.")

    try:
        llm = create_llm_func(model, temperature)
    except Exception as e:
        logging.error(f"Error creating LLM for {provider} - {model}: {e}")
        # Re-raise the error or raise a custom error, e.g., LLMCreationError(f"Failed to create LLM for {provider} - {model}: {e}")
        raise
    finally:
        # Ensure environment is restored even if LLM creation fails
        restore_environment()

    return llm

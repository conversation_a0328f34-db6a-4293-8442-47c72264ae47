@echo off
echo ========================================
echo    CrewAI-Studio avec LM Studio
echo ========================================
echo.

echo Etape 1: Verification de LM Studio...
echo IMPORTANT: Assurez-vous que LM Studio est demarré avec votre modele charge !
echo.
pause

echo Etape 2: Demarrage du proxy litellm...
echo.
start "LiteLLM Proxy" cmd /k "litellm --model openai/gpt-3.5-turbo --api_base http://localhost:1234/v1 --api_key lm-studio --port 8000 --host 0.0.0.0"
echo Proxy litellm demarre en arriere-plan...
echo.

echo Attente de 5 secondes pour que le proxy se lance...
timeout /t 5 /nobreak > nul

echo Etape 3: Demarrage de CrewAI-Studio...
echo.
echo CrewAI-Studio va s'ouvrir dans votre navigateur...
echo.
streamlit run app/app.py

echo.
echo Fermeture du systeme...
pause

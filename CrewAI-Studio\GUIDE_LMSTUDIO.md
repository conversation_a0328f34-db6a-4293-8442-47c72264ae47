# Guide d'utilisation de LM Studio avec CrewAI-Studio

## Configuration automatique terminée ✅

Votre système est maintenant configuré pour utiliser LM Studio avec CrewAI-Studio via litellm comme proxy.

## Étapes pour utiliser LM Studio

### 1. Installer les dépendances
```bash
pip install -r requirements.txt
```

### 2. Démarrer LM Studio
- Ouvrez LM Studio
- Chargez votre modèle Qwen3-14B-Q4_K_M.gguf
- Assurez-vous que le serveur local est démarré (généralement sur le port 1234)

### 3. Démarrer le proxy litellm
**Sur Windows :**
```bash
start_litellm.bat
```

**Sur Linux/Mac :**
```bash
chmod +x start_litellm.sh
./start_litellm.sh
```

### 4. Lancer CrewAI-Studio
```bash
streamlit run app/app.py
```

### 5. Configuration dans l'interface
Lors de la création de vos agents :
- **Fournisseur LLM** : Sélectionnez "OpenAI"
- **Modèle** : Choisissez "gpt-3.5-turbo" ou "gpt-4" (ces noms seront redirigés vers votre modèle LM Studio)

## Alternative : Utilisation directe de LM Studio

Si vous préférez utiliser LM Studio directement sans litellm :
- **Fournisseur LLM** : Sélectionnez "LM Studio"
- **Modèle** : Sélectionnez "Qwen3-14B-Q4_K_M.gguf"

## Dépannage

### Problème : "Connection refused"
- Vérifiez que LM Studio est en cours d'exécution
- Vérifiez que le modèle est chargé dans LM Studio
- Vérifiez que le serveur local est activé dans LM Studio

### Problème : "API key not found"
- Le fichier .env contient déjà une clé factice
- Aucune vraie clé OpenAI n'est nécessaire

### Problème : litellm ne démarre pas
- Assurez-vous d'avoir installé litellm : `pip install litellm`
- Vérifiez que le port 8000 n'est pas déjà utilisé

## Ports utilisés
- **LM Studio** : 1234 (par défaut)
- **Proxy litellm** : 8000
- **CrewAI-Studio** : 8501 (Streamlit par défaut)

## Avantages de cette configuration
- ✅ Utilisation de modèles locaux (confidentialité)
- ✅ Pas besoin de clé API OpenAI payante
- ✅ Compatible avec l'interface CrewAI-Studio existante
- ✅ Flexibilité pour changer de modèle dans LM Studio
